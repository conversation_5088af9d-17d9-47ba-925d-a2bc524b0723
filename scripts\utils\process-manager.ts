/**
 * Process Manager for Missing Document Reminder Scheduler
 *
 * Provides start/stop/restart capabilities for the long-running scheduler process
 * with proper process management and monitoring.
 *
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2025-07-14
 */

import * as fs from 'fs';
import * as path from 'path';
import { spawn, ChildProcess } from 'child_process';
import { ReminderLogger } from './reminder-logger';

export interface ProcessInfo {
  pid: number;
  startTime: Date;
  status: 'running' | 'stopped' | 'error';
  command: string;
  args: string[];
}

export interface ProcessManagerConfig {
  pidFile: string;
  logFile: string;
  maxRestartAttempts: number;
  restartDelay: number; // milliseconds
  healthCheckInterval: number; // milliseconds
}

/**
 * Process Manager for the scheduler
 */
export class ProcessManager {
  private readonly logger = new ReminderLogger();
  private readonly config: ProcessManagerConfig;
  private process: ChildProcess | null = null;
  private restartAttempts = 0;
  private healthCheckTimer: NodeJS.Timeout | null = null;

  constructor(config?: Partial<ProcessManagerConfig>) {
    const defaultConfig: ProcessManagerConfig = {
      pidFile: path.join(process.cwd(), 'logs', 'reminders', 'scheduler.pid'),
      logFile: path.join(process.cwd(), 'logs', 'reminders', 'scheduler.log'),
      maxRestartAttempts: 3,
      restartDelay: 5000, // 5 seconds
      healthCheckInterval: 30000, // 30 seconds
    };

    this.config = { ...defaultConfig, ...config };
    this.ensureDirectories();
  }

  /**
   * Start the scheduler process
   */
  async start(): Promise<boolean> {
    try {
      // Check if already running
      if (await this.isRunning()) {
        this.logger.warn('Scheduler is already running', 'ProcessManager');
        return false;
      }

      this.logger.info('Starting scheduler process', 'ProcessManager');

      // Spawn the scheduler process
      const scriptPath = path.join(
        process.cwd(),
        'scripts',
        'missing-document-reminder.ts',
      );

      // Use npx ts-node for better cross-platform compatibility
      const isWindows = process.platform === 'win32';
      const command = isWindows ? 'npx' : 'ts-node';
      const args = isWindows
        ? ['ts-node', scriptPath, '--scheduler']
        : [scriptPath, '--scheduler'];

      this.process = spawn(command, args, {
        detached: true,
        stdio: ['ignore', 'pipe', 'pipe'],
        env: { ...process.env },
        shell: isWindows, // Use shell on Windows for better compatibility
      });

      if (!this.process.pid) {
        throw new Error('Failed to start process - no PID assigned');
      }

      // Write PID file
      await this.writePidFile(this.process.pid);

      // Setup process event handlers
      this.setupProcessHandlers();

      // Start health monitoring
      this.startHealthCheck();

      this.logger.info(
        'Scheduler process started successfully',
        'ProcessManager',
        {
          pid: this.process.pid,
          command,
          args,
        },
      );

      return true;
    } catch (error) {
      this.logger.error(
        'Failed to start scheduler process',
        'ProcessManager',
        error.stack,
        {
          error: error.message,
        },
      );
      return false;
    }
  }

  /**
   * Stop the scheduler process
   */
  async stop(): Promise<boolean> {
    try {
      const processInfo = await this.getProcessInfo();

      if (!processInfo || processInfo.status !== 'running') {
        this.logger.warn(
          'No running scheduler process found',
          'ProcessManager',
        );
        await this.cleanup();
        return true;
      }

      this.logger.info('Stopping scheduler process', 'ProcessManager', {
        pid: processInfo.pid,
      });

      // Stop health monitoring
      this.stopHealthCheck();

      // Try graceful shutdown first
      if (this.process) {
        this.process.kill('SIGTERM');

        // Wait for graceful shutdown
        await this.waitForProcessExit(5000);

        // Force kill if still running
        if (this.process && !this.process.killed) {
          this.process.kill('SIGKILL');
          await this.waitForProcessExit(2000);
        }
      } else {
        // Process not managed by this instance, try to kill by PID
        try {
          process.kill(processInfo.pid, 'SIGTERM');
          await new Promise((resolve) => setTimeout(resolve, 5000));

          // Check if still running and force kill
          if (await this.isProcessRunning(processInfo.pid)) {
            process.kill(processInfo.pid, 'SIGKILL');
          }
        } catch (killError) {
          // Process might already be dead
          this.logger.warn(
            'Failed to kill process, might already be stopped',
            'ProcessManager',
            {
              pid: processInfo.pid,
              error: killError.message,
            },
          );
        }
      }

      await this.cleanup();

      this.logger.info(
        'Scheduler process stopped successfully',
        'ProcessManager',
      );
      return true;
    } catch (error) {
      this.logger.error(
        'Failed to stop scheduler process',
        'ProcessManager',
        error.stack,
        {
          error: error.message,
        },
      );
      return false;
    }
  }

  /**
   * Restart the scheduler process
   */
  async restart(): Promise<boolean> {
    try {
      this.logger.info('Restarting scheduler process', 'ProcessManager');

      const stopResult = await this.stop();
      if (!stopResult) {
        this.logger.error(
          'Failed to stop process during restart',
          'ProcessManager',
        );
        return false;
      }

      // Wait a moment before restarting
      await new Promise((resolve) =>
        setTimeout(resolve, this.config.restartDelay),
      );

      const startResult = await this.start();
      if (!startResult) {
        this.logger.error(
          'Failed to start process during restart',
          'ProcessManager',
        );
        return false;
      }

      this.restartAttempts = 0; // Reset restart attempts on successful restart
      this.logger.info(
        'Scheduler process restarted successfully',
        'ProcessManager',
      );
      return true;
    } catch (error) {
      this.logger.error(
        'Failed to restart scheduler process',
        'ProcessManager',
        error.stack,
        {
          error: error.message,
        },
      );
      return false;
    }
  }

  /**
   * Get current process information
   */
  async getProcessInfo(): Promise<ProcessInfo | null> {
    try {
      const pid = await this.readPidFile();
      if (!pid) {
        return null;
      }

      const isRunning = await this.isProcessRunning(pid);
      const status = isRunning ? 'running' : 'stopped';

      const isWindows = process.platform === 'win32';
      const command = isWindows ? 'npx' : 'ts-node';
      const args = isWindows
        ? ['ts-node', 'scripts/missing-document-reminder.ts', '--scheduler']
        : ['scripts/missing-document-reminder.ts', '--scheduler'];

      return {
        pid,
        startTime: await this.getProcessStartTime(pid),
        status,
        command,
        args,
      };
    } catch (error) {
      this.logger.error(
        'Failed to get process info',
        'ProcessManager',
        error.stack,
      );
      return null;
    }
  }

  /**
   * Check if scheduler is currently running
   */
  async isRunning(): Promise<boolean> {
    const processInfo = await this.getProcessInfo();
    return processInfo?.status === 'running';
  }

  /**
   * Setup process event handlers
   */
  private setupProcessHandlers(): void {
    if (!this.process) return;

    this.process.on('exit', (code, signal) => {
      this.logger.info('Scheduler process exited', 'ProcessManager', {
        code,
        signal,
        pid: this.process?.pid,
      });

      // Attempt restart if unexpected exit
      if (code !== 0 && this.restartAttempts < this.config.maxRestartAttempts) {
        this.restartAttempts++;
        this.logger.warn(
          `Attempting restart ${this.restartAttempts}/${this.config.maxRestartAttempts}`,
          'ProcessManager',
        );

        setTimeout(() => {
          this.start();
        }, this.config.restartDelay);
      } else if (this.restartAttempts >= this.config.maxRestartAttempts) {
        this.logger.error(
          'Maximum restart attempts reached, giving up',
          'ProcessManager',
        );
        this.cleanup();
      }
    });

    this.process.on('error', (error) => {
      this.logger.error(
        'Scheduler process error',
        'ProcessManager',
        error.stack,
        {
          error: error.message,
          pid: this.process?.pid,
        },
      );
    });

    // Pipe stdout and stderr to log file
    if (this.process.stdout) {
      this.process.stdout.on('data', (data) => {
        this.appendToLogFile(`[STDOUT] ${data.toString()}`);
      });
    }

    if (this.process.stderr) {
      this.process.stderr.on('data', (data) => {
        this.appendToLogFile(`[STDERR] ${data.toString()}`);
      });
    }
  }

  /**
   * Start health monitoring
   */
  private startHealthCheck(): void {
    this.healthCheckTimer = setInterval(async () => {
      const isRunning = await this.isRunning();
      if (!isRunning) {
        this.logger.warn(
          'Health check failed - process not running',
          'ProcessManager',
        );
        this.stopHealthCheck();
      }
    }, this.config.healthCheckInterval);
  }

  /**
   * Stop health monitoring
   */
  private stopHealthCheck(): void {
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
      this.healthCheckTimer = null;
    }
  }

  /**
   * Wait for process to exit
   */
  private async waitForProcessExit(timeout: number): Promise<void> {
    return new Promise((resolve) => {
      if (!this.process) {
        resolve();
        return;
      }

      const timer = setTimeout(() => {
        resolve();
      }, timeout);

      this.process.on('exit', () => {
        clearTimeout(timer);
        resolve();
      });
    });
  }

  /**
   * Check if a process is running by PID
   */
  private async isProcessRunning(pid: number): Promise<boolean> {
    try {
      process.kill(pid, 0); // Signal 0 checks if process exists
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get process start time
   */
  private async getProcessStartTime(pid: number): Promise<Date> {
    try {
      // On Windows, we can't easily get process start time from /proc
      // For now, we'll use the PID file creation time as an approximation
      if (process.platform === 'win32') {
        if (fs.existsSync(this.config.pidFile)) {
          const stats = fs.statSync(this.config.pidFile);
          return stats.birthtime || stats.ctime;
        }
      } else {
        // On Unix-like systems, try to get from /proc
        const stats = fs.statSync(`/proc/${pid}`);
        return stats.birthtime || stats.ctime;
      }
    } catch (error) {
      // Fallback to current time if can't determine start time
    }
    return new Date();
  }

  /**
   * Write PID to file
   */
  private async writePidFile(pid: number): Promise<void> {
    fs.writeFileSync(this.config.pidFile, pid.toString());
  }

  /**
   * Read PID from file
   */
  private async readPidFile(): Promise<number | null> {
    try {
      if (!fs.existsSync(this.config.pidFile)) {
        return null;
      }
      const pidStr = fs.readFileSync(this.config.pidFile, 'utf8').trim();
      return parseInt(pidStr, 10);
    } catch (error) {
      return null;
    }
  }

  /**
   * Append to log file
   */
  private appendToLogFile(content: string): void {
    try {
      const timestamp = new Date().toISOString();
      fs.appendFileSync(this.config.logFile, `[${timestamp}] ${content}`);
    } catch (error) {
      // Ignore log file errors to prevent infinite loops
    }
  }

  /**
   * Clean up process files
   */
  private async cleanup(): Promise<void> {
    try {
      if (fs.existsSync(this.config.pidFile)) {
        fs.unlinkSync(this.config.pidFile);
      }
      this.process = null;
      this.stopHealthCheck();
    } catch (error) {
      this.logger.error(
        'Failed to cleanup process files',
        'ProcessManager',
        error.stack,
      );
    }
  }

  /**
   * Ensure required directories exist
   */
  private ensureDirectories(): void {
    const pidDir = path.dirname(this.config.pidFile);
    const logDir = path.dirname(this.config.logFile);

    if (!fs.existsSync(pidDir)) {
      fs.mkdirSync(pidDir, { recursive: true });
    }

    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }
  }
}
