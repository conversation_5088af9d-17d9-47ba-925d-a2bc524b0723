/**
 * Missing Document Reminder Service Unit Tests
 *
 * Comprehensive unit tests covering database queries, date calculations,
 * email sending scenarios, and error handling cases.
 *
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2025-07-14
 */

import { Test, TestingModule } from '@nestjs/testing';
import { ApplicationStatus } from '@prisma/client';
import { MissingDocumentReminderService } from '../../scripts/missing-document-reminder.service';
import { PrismaService } from '../../src/utils/prisma.service';
import { LoggerService } from '../../src/utils/logger.service';
import { NotificationSettingsStorageService } from '../../src/utils/notification-settings-storage.service';

describe('MissingDocumentReminderService', () => {
  let service: MissingDocumentReminderService;
  let prismaService: jest.Mocked<PrismaService>;
  let loggerService: jest.Mocked<LoggerService>;
  let notificationSettingsService: jest.Mocked<NotificationSettingsStorageService>;

  const mockPrismaService = {
    application: {
      findMany: jest.fn(),
    },
  };

  const mockLoggerService = {
    error: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  };

  const mockNotificationSettingsService = {
    getSettings: jest.fn(),
    readSettings: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MissingDocumentReminderService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: LoggerService,
          useValue: mockLoggerService,
        },
        {
          provide: NotificationSettingsStorageService,
          useValue: mockNotificationSettingsService,
        },
      ],
    }).compile();

    service = module.get<MissingDocumentReminderService>(
      MissingDocumentReminderService,
    );
    prismaService = module.get(PrismaService);
    loggerService = module.get(LoggerService);
    notificationSettingsService = module.get(
      NotificationSettingsStorageService,
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.useRealTimers();
  });

  describe('findApplicationsWithMissingDocuments', () => {
    it('should find applications with pending documents', async () => {
      // Arrange
      const mockApplications = [
        {
          id: 'app-1',
          application_number: 'APP-2024-001',
          service_type: 'immigration',
          status: ApplicationStatus.Pending,
          user_id: 'user-1',
          guest_name: null,
          guest_email: null,
          created_at: new Date('2024-01-01'),
          updated_at: new Date('2024-01-08'), // 7 days ago from test date
          user: {
            id: 'user-1',
            name: 'John Doe',
            email: '<EMAIL>',
          },
          documents: [
            {
              id: 'doc-1',
              file_name: 'Passport Copy',
              required: true,
              status: 'pending',
              stage_order: 1,
              request_reason: 'Required for identity verification',
            },
          ],
        },
      ];

      mockPrismaService.application.findMany.mockResolvedValue(
        mockApplications,
      );
      mockNotificationSettingsService.getSettings.mockResolvedValue({
        missing_document_reminder_days: 7,
      });

      // Mock current date to be 7 days after the updated_at date
      const mockDate = new Date('2024-01-15');
      jest.spyOn(Date, 'now').mockReturnValue(mockDate.getTime());
      jest.useFakeTimers();
      jest.setSystemTime(mockDate);

      // Act
      const result = await service.findApplicationsWithMissingDocuments();

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0]).toMatchObject({
        id: 'app-1',
        application_number: 'APP-2024-001',
        daysSinceLastUpdate: 7,
        reminderDays: 7,
        missingDocuments: [
          {
            id: 'doc-1',
            file_name: 'Passport Copy',
            required: true,
            status: 'pending',
          },
        ],
      });

      expect(mockPrismaService.application.findMany).toHaveBeenCalledWith({
        where: {
          status: {
            not: ApplicationStatus.Completed,
          },
          documents: {
            some: {
              OR: [
                { status: 'pending' },
                { status: 'rejected' },
                { file_url: '' },
              ],
            },
          },
        },
        include: {
          user: {
            select: { id: true, name: true, email: true },
          },
          documents: {
            where: {
              OR: [
                { status: 'pending' },
                { status: 'rejected' },
                { file_url: '' },
              ],
            },
            orderBy: { stage_order: 'asc' },
          },
        },
        orderBy: { updated_at: 'desc' },
      });
    });

    it('should exclude applications older than 30 days', async () => {
      // Arrange
      const mockApplications = [
        {
          id: 'app-1',
          application_number: 'APP-2024-001',
          service_type: 'immigration',
          status: ApplicationStatus.Pending,
          user_id: 'user-1',
          guest_name: null,
          guest_email: null,
          created_at: new Date('2024-01-01'),
          updated_at: new Date('2023-12-01'), // 45 days ago
          user: {
            id: 'user-1',
            name: 'John Doe',
            email: '<EMAIL>',
          },
          documents: [
            {
              id: 'doc-1',
              file_name: 'Passport Copy',
              required: true,
              status: 'pending',
              stage_order: 1,
              request_reason: 'Required for identity verification',
            },
          ],
        },
      ];

      mockPrismaService.application.findMany.mockResolvedValue(
        mockApplications,
      );
      mockNotificationSettingsService.getSettings.mockResolvedValue({
        missing_document_reminder_days: 7,
      });

      // Mock current date to be 45 days after the updated_at date
      const mockDate = new Date('2024-01-15');
      jest.spyOn(Date, 'now').mockReturnValue(mockDate.getTime());
      jest.useFakeTimers();
      jest.setSystemTime(mockDate);

      // Act
      const result = await service.findApplicationsWithMissingDocuments();

      // Assert
      expect(result).toHaveLength(0);
    });

    it('should exclude applications that do not match reminder timing', async () => {
      // Arrange
      const mockApplications = [
        {
          id: 'app-1',
          application_number: 'APP-2024-001',
          service_type: 'immigration',
          status: ApplicationStatus.Pending,
          user_id: 'user-1',
          guest_name: null,
          guest_email: null,
          created_at: new Date('2024-01-01'),
          updated_at: new Date('2024-01-10'), // 5 days ago
          user: {
            id: 'user-1',
            name: 'John Doe',
            email: '<EMAIL>',
          },
          documents: [
            {
              id: 'doc-1',
              file_name: 'Passport Copy',
              required: true,
              status: 'pending',
              stage_order: 1,
              request_reason: 'Required for identity verification',
            },
          ],
        },
      ];

      mockPrismaService.application.findMany.mockResolvedValue(
        mockApplications,
      );
      mockNotificationSettingsService.getSettings.mockResolvedValue({
        missing_document_reminder_days: 7, // Reminder set for 7 days, but only 5 days have passed
      });

      // Mock current date to be 5 days after the updated_at date
      const mockDate = new Date('2024-01-15');
      jest.spyOn(Date, 'now').mockReturnValue(mockDate.getTime());
      jest.useFakeTimers();
      jest.setSystemTime(mockDate);

      // Act
      const result = await service.findApplicationsWithMissingDocuments();

      // Assert
      expect(result).toHaveLength(0);
    });

    it('should handle database errors gracefully', async () => {
      // Arrange
      const dbError = new Error('Database connection failed');
      mockPrismaService.application.findMany.mockRejectedValue(dbError);

      // Act & Assert
      await expect(
        service.findApplicationsWithMissingDocuments(),
      ).rejects.toThrow(dbError);
      expect(mockLoggerService.error).toHaveBeenCalledWith(
        'Failed to find applications with missing documents: Database connection failed',
        expect.any(String),
        'MissingDocumentReminderService',
      );
    });

    it('should handle notification settings errors and use default', async () => {
      // Arrange
      const mockApplications = [
        {
          id: 'app-1',
          application_number: 'APP-2024-001',
          service_type: 'immigration',
          status: ApplicationStatus.Pending,
          user_id: 'user-1',
          guest_name: null,
          guest_email: null,
          created_at: new Date('2024-01-01'),
          updated_at: new Date('2024-01-08'), // 7 days ago
          user: {
            id: 'user-1',
            name: 'John Doe',
            email: '<EMAIL>',
          },
          documents: [
            {
              id: 'doc-1',
              file_name: 'Passport Copy',
              required: true,
              status: 'pending',
              stage_order: 1,
              request_reason: 'Required for identity verification',
            },
          ],
        },
      ];

      mockPrismaService.application.findMany.mockResolvedValue(
        mockApplications,
      );
      mockNotificationSettingsService.readSettings.mockRejectedValue(
        new Error('Settings not found'),
      );

      // Mock current date to be 7 days after the updated_at date
      const mockDate = new Date('2024-01-15');
      jest.spyOn(Date, 'now').mockReturnValue(mockDate.getTime());
      jest.useFakeTimers();
      jest.setSystemTime(mockDate);

      // Act
      const result = await service.findApplicationsWithMissingDocuments();

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].reminderDays).toBe(7); // Should use default value
      expect(mockLoggerService.error).toHaveBeenCalledWith(
        'Failed to get reminder days for user user-1: Settings not found',
        expect.any(String),
        'MissingDocumentReminderService',
      );
    });
  });

  describe('getRecipientEmail', () => {
    it('should return user email when available', () => {
      // Arrange
      const application = {
        user: { email: '<EMAIL>' },
        guest_email: '<EMAIL>',
        application_number: 'APP-001',
      } as any;

      // Act
      const result = service.getRecipientEmail(application);

      // Assert
      expect(result).toBe('<EMAIL>');
    });

    it('should return guest email when user email not available', () => {
      // Arrange
      const application = {
        user: null,
        guest_email: '<EMAIL>',
        application_number: 'APP-001',
      } as any;

      // Act
      const result = service.getRecipientEmail(application);

      // Assert
      expect(result).toBe('<EMAIL>');
    });

    it('should throw error when no email available', () => {
      // Arrange
      const application = {
        user: null,
        guest_email: null,
        application_number: 'APP-001',
      } as any;

      // Act & Assert
      expect(() => service.getRecipientEmail(application)).toThrow(
        'No email found for application APP-001',
      );
    });
  });

  describe('getRecipientName', () => {
    it('should return user name when available', () => {
      // Arrange
      const application = {
        user: { name: 'John Doe' },
        guest_name: 'Jane Smith',
      } as any;

      // Act
      const result = service.getRecipientName(application);

      // Assert
      expect(result).toBe('John Doe');
    });

    it('should return guest name when user name not available', () => {
      // Arrange
      const application = {
        user: null,
        guest_name: 'Jane Smith',
      } as any;

      // Act
      const result = service.getRecipientName(application);

      // Assert
      expect(result).toBe('Jane Smith');
    });

    it('should return default name when no name available', () => {
      // Arrange
      const application = {
        user: null,
        guest_name: null,
      } as any;

      // Act
      const result = service.getRecipientName(application);

      // Assert
      expect(result).toBe('User');
    });
  });
});
